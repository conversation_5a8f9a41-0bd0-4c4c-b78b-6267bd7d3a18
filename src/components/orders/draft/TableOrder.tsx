import { customersData } from "@/src/_mock/customers-data";
import { EditNotifications, Search } from "@mui/icons-material";

import { Download } from "@mui/icons-material";
import {
  Badge,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from "@mui/material";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import React, { useCallback, useEffect, useState } from "react";
import { formatMoney } from "@/src/utils/format-money";
import EditIcon from "@mui/icons-material/Edit";

import _ from "lodash";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useOrder } from "@/src/api/hooks/order/use-order";
import {
  getFilterOrderStatusText,
  getStatusDeliveryLabel,
  getStatusKeyFromTab,
  OrderProductPayStatusWithBg,
  OrderProductTransportStatusWithBg,
} from "@/src/utils/order/order-helper";
import dayjs, { Dayjs } from "dayjs";
import Link from "next/link";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { ClearIcon, DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import ActionButton from "@/src/components/common/ActionButton";
import TruncatedText from "../../truncated-text/truncated-text";
import { useValidImage } from "@/src/hooks/useValidImage";
import { useAppSelector } from "@/src/redux/hooks";
import { formatTruncatedText } from "@/src/utils/format";
import { TotalOrderStatusParamsDto } from "@/src/api/services/order/order.service";
interface TableOrderType {
  tabName: string;
  isGranted: any;
}

type ListOrderStatusOrderType =
  | "All"
  | "Pending"
  | "WaitingForDelivery"
  | "Delivering"
  | "Success"
  | "Failed"
  | "Refund";
type ListOrderStatusTransportOrderType = "All" | "WaitingForDelivery" | "Delivering" | "Success";

type ListOrderStatusPayType = "All" | "NotPaid" | "Paid" | "Refund";

const filterOrderStatus: ListOrderStatusOrderType[] = [
  "All",
  "Pending",
  "WaitingForDelivery",
  "Delivering",
  "Success",
  "Failed",
  "Refund",
];

export interface TotalOrderStatusResponseDto {
  all: number;
  pendding: number;
  waitingForDelivery: number;
  delivering: number;
  success: number;
  failed: number;
  refund: number;
}

const dateOptions = [
  { label: "Hôm nay", value: "today" },
  { label: "7 ngày", value: "7days" },
  { label: "1 tháng", value: "1month" },
  { label: "3 tháng", value: "3months" },
  { label: "6 tháng", value: "6months" },
  { label: "Khác", value: "other" },
];

export interface TableOrderFilterType {
  shopId: string;
  search: string;
  itemsType?: "Service" | "Product";
  statusOrder: ListOrderStatusOrderType;
  statusTransport: ListOrderStatusTransportOrderType;
  statusPay: ListOrderStatusPayType;
  userId?: string;
  fromDate?: string;
  toDate?: string;
}

const initFilterData: TableOrderFilterType = {
  shopId: "",
  search: "",
  itemsType: "Product",
  statusOrder: "All",
  statusTransport: "All",
  statusPay: "All",
};
export default function TableOrder({ tabName, isGranted }: TableOrderType) {
  const { listOrder, loading } = useOrder();
  const pathname = usePathname();
  const storeId = useStoreId();
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const router = useRouter();
  const { printTransportOrders, exportOrderExcel, totalOrderStatus } = useOrder();
  const [printing, setPrinting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [selectedRange, setSelectedRange] = useState("today");
  const [totalOrderByStatus, setTotalOrderByStatus] = useState<TotalOrderStatusResponseDto>();
  const [orderDateRange, setOrderDateRange] = useState<[Dayjs | null, Dayjs | null]>([
    dayjs().startOf("day"),
    dayjs().endOf("day"),
  ]);
  const getDateRangeByValue = (value: string): [Dayjs | null, Dayjs | null] => {
    const now = dayjs();
    switch (value) {
      case "today":
        return [now.startOf("day"), now.endOf("day")];
      case "7days":
        return [now.subtract(6, "day").startOf("day"), now.endOf("day")];
      case "1month":
        return [now.subtract(1, "month").startOf("day"), now.endOf("day")];
      case "3months":
        return [now.subtract(3, "month").startOf("day"), now.endOf("day")];
      case "6months":
        return [now.subtract(6, "month").startOf("day"), now.endOf("day")];
      case "other":
      default:
        return [null, null];
    }
  };

  const { query } = router;
  useEffect(() => {
    if (query && query.userId) {
      setFilterData((prevState) => ({
        ...prevState,
        userId: query.userId as string,
      }));
    }
  }, [query]);
  const [orders, setOrders] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [filterData, setFilterData] = useState<TableOrderFilterType>(initFilterData);

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelected(orders.map((c) => c.orderId));
    } else {
      setSelected([]);
    }
  };
  const handleSelectOne = (event, id) => {
    if (event.target.checked) {
      setSelected((prevSelected) => [...prevSelected, id]);
    } else {
      setSelected((prevSelected) => prevSelected.filter((item) => item !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleClickFilterStatus = (orderStatus) => {
    setPage(0);
    setSelectedRange("today");
    setOrderDateRange([null, null]);
    setFilterData((prevState) => ({
      ...prevState,
      statusOrder: orderStatus,
      search: "",
      fromDate: null,
      toDate: null,
    }));
  };

  const handleClickEdit = (orderId) => {
    router.push(`${paths.orders.detail}?id=${orderId}`);
  };

  const fetchOrderList = async (currentPage, pageSize, data) => {
    const skip = currentPage * pageSize; // Always search from the beginning if there's a search query
    const limit = pageSize;

    // Normalize the search query if it starts with '0'

    const response = await listOrder(skip, limit, data);

    if (response && response.data) {
      setOrders(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchOrderList with debounce
  const debouncedFetchOrderList = useCallback(
    _.debounce((currentPage, pageSize, data) => {
      fetchOrderList(currentPage, pageSize, data);
    }, 400), // Delay 400ms
    []
  );

  const handleClickPrintTransportOrder = async () => {
    setPrinting(true);
    try {
      const response = await printTransportOrders({ shopId: storeId, orderIds: selected }); // axios hoặc fetch

      if (response?.data) {
        const blob = new Blob([response.data], {
          type: "application/pdf",
        });

        const url = window.URL.createObjectURL(blob);
        // Format ngày với dayjs: dd-MM-yyyy
        const formattedDate = dayjs().format("DD-MM-YYYY");

        const link = document.createElement("a");
        link.href = url;
        link.download = `PhieuVanChuyen_${formattedDate}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Tuỳ chọn: thu hồi URL sau một khoảng thời gian
        setTimeout(() => URL.revokeObjectURL(url), 1000 * 60); // 1 phút sau
      }
    } catch (error) {
      console.error("Print PDF failed:", error);
    } finally {
      setPrinting(false);
    }
  };

  const handleExportOrderExcel = async () => {
    setExporting(true);
    try {
      const response = await exportOrderExcel(filterData); // axios hoặc fetch
      if (response) {
        const blob = new Blob([response.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "DanhSachDonHang.xlsx"); // tên file
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Export Excel failed:", error);
    } finally {
      setExporting(false);
    }
  };

  const handleSelectChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    setSelectedRange(value);

    if (value === "other") {
      setOrderDateRange([null, null]);
      setFilterData((prev) => ({
        ...prev,
        fromDate: undefined,
        toDate: undefined,
      }));
    } else {
      const [from, to] = getDateRangeByValue(value);
      setOrderDateRange([from, to]);
      setFilterData((prev) => ({
        ...prev,
        fromDate: from?.format("YYYY-MM-DD HH:mm:ss"),
        toDate: to?.format("YYYY-MM-DD HH:mm:ss"),
      }));
    }
    setPage(0);
  };

  const handleChangeOrderFromDate = (newValue: Dayjs | null) => {
    setPage(0);
    if (!newValue) {
      setOrderDateRange([null, null]);
      setFilterData((prev) => ({ ...prev, fromDate: null, toDate: null }));
      setSelectedRange("other");
      return;
    }

    let updatedEndDate = orderDateRange[1];
    if (!updatedEndDate || newValue.isAfter(updatedEndDate)) {
      updatedEndDate = newValue;
    }

    setOrderDateRange([newValue, updatedEndDate]);
    setFilterData((prev) => ({
      ...prev,
      fromDate: newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
      toDate: updatedEndDate.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
    }));
    setSelectedRange("other");
  };

  const handleChangeOrderToDate = (newValue: Dayjs | null) => {
    setPage(0);
    if (!newValue) {
      setOrderDateRange([null, null]);
      setFilterData((prev) => ({ ...prev, fromDate: null, toDate: null }));
      setSelectedRange("other");
      return;
    }

    let updatedStartDate = orderDateRange[0];
    if (!updatedStartDate || newValue.isBefore(updatedStartDate)) {
      updatedStartDate = newValue;
    }

    setOrderDateRange([updatedStartDate, newValue]);
    setFilterData((prev) => ({
      ...prev,
      fromDate: updatedStartDate.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
      toDate: newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
    }));
    setSelectedRange("other");
  };

  useEffect(() => {
    if (selectedRange !== "other") {
      const [from, to] = getDateRangeByValue(selectedRange);
      setOrderDateRange([from, to]);
      setFilterData((prev) => ({
        ...prev,
        fromDate: from?.format("YYYY-MM-DD HH:mm:ss"),
        toDate: to?.format("YYYY-MM-DD HH:mm:ss"),
      }));
    }
  }, [selectedRange, filterData.statusOrder]);

  useEffect(() => {
    if (filterData.shopId) {
      debouncedFetchOrderList(page, rowsPerPage, filterData);
    }

    return () => {
      debouncedFetchOrderList.cancel();
    };
  }, [page, rowsPerPage, debouncedFetchOrderList, filterData]);

  useEffect(() => {
    if (storeId) {
      setFilterData((prevState) => {
        return { ...prevState, shopId: storeId };
      });
    }
  }, [storeId]);

  const handleChangeSearch = (e) => {
    setFilterData((prevState) => {
      return { ...prevState, search: e.target.value };
    });
    setPage(0);
  };

  const fetchOrderQuantityByStatus = async () => {
    try {
      if (storeId) {
        const data: TotalOrderStatusParamsDto = {
          shopId: storeId,
          statusOrder: filterData.statusOrder,
          fromDate: filterData.fromDate,
          toDate: filterData.toDate,
          search: filterData.search,
        };
        const response = await totalOrderStatus(data);
        if (response?.status === 200) {
          setTotalOrderByStatus(response?.data?.data || {});
        } else {
        }
      }
    } catch (error) {}
  };
  useEffect(() => {
    fetchOrderQuantityByStatus();
  }, [filterData]);

  return (
    <Box>
      <Box
        display="flex"
        gap={{ xs: 1, sm: 2 }}
        marginBottom={2}
        sx={{
          pt: 2,
          cursor: "pointer",
          overflowX: "auto",
          "&::-webkit-scrollbar": {
            display: "none",
          },
          scrollbarWidth: "none",
          pb: { xs: 1, sm: 0 },
        }}
      >
        {filterOrderStatus.map((orderStatus, index) => {
          const isSelected = orderStatus === filterData.statusOrder;
          const statusKey = getStatusKeyFromTab(orderStatus);
          const badgeCount = totalOrderByStatus?.[statusKey] ?? 0;
          return (
            <Badge
              badgeContent={badgeCount}
              color="primary"
              sx={{
                "& .MuiBadge-badge": {
                  fontSize: "0.7rem",
                  height: 18,
                  minWidth: 18,
                  zIndex: 99999,
                },
              }}
            >
              <Box
                key={index}
                sx={{
                  backgroundColor: "neutral.50",
                  paddingTop: { xs: 0.75, sm: 1 },
                  paddingBottom: { xs: 0.75, sm: 1 },
                  paddingLeft: { xs: 1.5, sm: 2 },
                  paddingRight: { xs: 1.5, sm: 2 },
                  borderRadius: 1,
                  border: isSelected ? `1px solid #2654FE` : "none",
                  minWidth: "max-content",
                  whiteSpace: "nowrap",
                }}
                onClick={() => handleClickFilterStatus(orderStatus)}
              >
                <Typography
                  color={isSelected ? "#2654FE" : "inherit"}
                  sx={{
                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                  }}
                >
                  {getFilterOrderStatusText(orderStatus)}
                </Typography>
              </Box>
            </Badge>
          );
        })}
      </Box>
      <Box
        sx={{ mb: 3 }}
        display="flex"
        justifyContent="space-between"
        flexDirection={{ xs: "column", md: "row" }}
        gap={2}
      >
        <Box display="flex" alignItems="center" gap={2} flexDirection={{ xs: "column", md: "row" }}>
          <Select
            size="small"
            value={selectedRange}
            onChange={handleSelectChange}
            sx={{
              width: { xs: "100%", sm: 140 },
              borderRadius: "8px",
              "& .MuiSelect-select": {
                padding: "1px 16px",
                height: 40,
                display: "flex",
                alignItems: "center",
              },
            }}
          >
            {dateOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
            <Box sx={{ display: "flex", gap: 0.5, alignItems: "center" }}>
              <DatePicker
                value={orderDateRange[0]}
                format="DD/MM/YYYY"
                onChange={handleChangeOrderFromDate}
                slotProps={{
                  textField: {
                    sx: {
                      "& .MuiOutlinedInput-root": {
                        height: { xs: 40, sm: 40 },
                        borderRadius: "8px",
                      },
                    },
                    InputProps: { sx: { height: 45, width: 180 } },
                  },
                }}
              />
              <Typography sx={{ mx: 0.5 }}>-</Typography>
              <DatePicker
                value={orderDateRange[1]}
                format="DD/MM/YYYY"
                onChange={handleChangeOrderToDate}
                shouldDisableDate={(date) => date.isBefore(orderDateRange[0], "day")}
                slotProps={{
                  textField: {
                    sx: {
                      "& .MuiOutlinedInput-root": {
                        height: { xs: 40, sm: 40 },
                        borderRadius: "8px",
                      },
                    },
                    InputProps: { sx: { height: 40, width: 180 } },
                  },
                }}
              />
              {(orderDateRange[0] || orderDateRange[1]) && (
                <IconButton
                  aria-label="clear-date"
                  onClick={() => {
                    setPage(0);
                    setSelectedRange("other");
                    setOrderDateRange([null, null]);
                    setFilterData((prev) => ({
                      ...prev,
                      fromDate: undefined,
                      toDate: undefined,
                    }));
                  }}
                  size="small"
                  sx={{ mt: -0.5 }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </LocalizationProvider>
          <TextField
            placeholder="Mã đơn, sđt, tên khách hàng"
            variant="outlined"
            size="small"
            value={filterData.search}
            sx={{ width: { xs: "100%", sm: "300px" } }}
            onChange={handleChangeSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </Box>
        <ActionButton
          permission={PERMISSION_TYPE_ENUM.Export}
          tooltip="Bạn không có quyền export"
          isGranted={isGranted}
          pathname={pathname}
          onClick={handleExportOrderExcel}
          loading={exporting}
          startIcon={<Download sx={{ marginRight: 0.5 }} />}
          variant="outlined"
          sx={{
            width: { xs: "50%", md: "auto" },
            whiteSpace: "nowrap",
          }}
        >
          Export
        </ActionButton>
      </Box>
      {selected.length > 0 && (
        <Stack sx={{ mb: 2 }} direction="row" spacing={2}>
          <Button
            variant="contained"
            onClick={handleClickPrintTransportOrder}
            disabled={printing}
            startIcon={printing ? <CircularProgress size={20} color="inherit" /> : null}
          >
            Tải vận đơn
          </Button>
        </Stack>
      )}
      <Box sx={{ width: "100%", overflowX: "auto" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={orders.length > 0 && selected.length === orders.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell sx={{ minWidth: "20px" }}>STT</TableCell>
              <TableCell sx={{ width: "180px" }}>Mã đơn hàng</TableCell>
              <TableCell sx={{ minWidth: "180px" }}>Sản phẩm</TableCell>
              <TableCell sx={{ minWidth: "210px" }}>Thời gian đặt hàng</TableCell>
              <TableCell sx={{ minWidth: "200px" }}>Khách hàng</TableCell>
              <TableCell sx={{ minWidth: "180px" }}>Tổng tiền</TableCell>
              <TableCell sx={{ minWidth: "180px" }}>Trạng thái vận chuyển</TableCell>
              <TableCell sx={{ minWidth: "180px" }}>Trạng thái thanh toán</TableCell>
              <TableCell sx={{ minWidth: "150px" }}>Nguồn đặt hàng</TableCell>
              <TableCell sx={{ minWidth: "200px" }}>Phương thức giao hàng</TableCell>
              <TableCell sx={{ minWidth: "150px" }}>Mã vận chuyển</TableCell>
              <TableCell
                sx={{
                  position: "sticky",
                  right: 0,
                  bottom: 0,
                  backgroundColor: "#fff",
                  zIndex: 3,
                  boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                  padding: { xs: "16px 4px", sm: "20px 16px" },
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  width: { xs: "70px", sm: "90px" },
                }}
              >
                Quản lý
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {orders.length > 0 ? (
              orders?.map((c, index) => {
                return (
                  <TableRow key={c.orderId}>
                    <TableCell sx={{ padding: 0 }}>
                      <Checkbox
                        checked={selected.includes(c.orderId)}
                        onChange={(event) => handleSelectOne(event, c.orderId)}
                      />
                    </TableCell>
                    <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell>
                      <TruncatedText
                        {...formatTruncatedText({
                          text: c.orderNo,
                          isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                          openInNewTab: false,
                          actionNeed: () => handleClickEdit(c.orderId),
                          width: "180px",
                        })}
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip
                        title={
                          <Box p={1}>
                            <ListItemHover listItems={c.listItems} />
                          </Box>
                        }
                      >
                        <Box>
                          <ListItemsOrderTable listItems={c.listItems} />
                        </Box>
                      </Tooltip>
                    </TableCell>
                    <TableCell>{dayjs(c.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                    <TableCell>{c?.creator?.fullName || "Khách hàng vãng lai"}</TableCell>
                    <TableCell>{formatMoney(c.totalAfterTax || 0)}đ</TableCell>
                    <TableCell>
                      <OrderProductTransportStatusWithBg status={c?.statusTransport} />
                    </TableCell>
                    <TableCell>
                      <OrderProductPayStatusWithBg status={c?.statusPay} />
                    </TableCell>
                    <TableCell>{c.partnerId ? "Web partner" : "Zalo Mini App"}</TableCell>
                    <TableCell>
                      {getStatusDeliveryLabel(c?.statusDelivery, c?.transportService)}
                    </TableCell>
                    <TableCell>{c?.transportOrderId}</TableCell>
                    <TableCell
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "start",
                        position: "sticky",
                        right: 0,
                        backgroundColor: "#fff",
                        zIndex: 2,
                        padding: "25px 16px",
                        boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                      }}
                    >
                      <Tooltip
                        title={
                          !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                            ? "Bạn không có quyền sửa"
                            : "Sửa đơn hàng"
                        }
                      >
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => handleClickEdit(c.orderId)}
                            disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={12} align="center">
                  Không có đơn hàng
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Box>
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={rowPerPageOptionsDefault}
        labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
      />
    </Box>
  );
}

export const ListItemsOrderTable = ({ listItems }) => {
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  // Lấy 2 sản phẩm đầu tiên
  const firstTwoItems = listItems.slice(0, 2);

  // Số lượng sản phẩm còn lại
  const remainingCount = listItems.length - 2;
  return (
    <Box display="flex" gap={1} alignItems="center">
      <Box display="flex" gap={1}>
        {firstTwoItems.map((item) => {
          const imgItemSrc = useValidImage(item.images?.[0]?.link, currentShop?.shopLogo?.link);
          const imgVariantSrc = useValidImage(item.variantImage?.link, imgItemSrc);
          return (
            <ItemThumbnail
              key={item.itemsId}
              url={imgVariantSrc}
              alt={item.itemsName}
              quantity={item.quantity}
            />
          );
        })}
      </Box>
      {remainingCount > 0 && (
        <Box
          sx={{
            backgroundColor: "neutral.50",
            p: 1,
            width: 40,
            height: 40,
            display: "flex", // Sử dụng Flexbox
            alignItems: "center", // Căn giữa theo chiều dọc
            justifyContent: "center",
            borderRadius: 1,
          }}
        >
          <Typography variant="body2" color="textSecondary">
            +{remainingCount}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

const ItemThumbnail = ({ url, alt, quantity }: { url: string; alt: string; quantity: number }) => {
  return (
    <Box
      sx={{
        width: 40, // Kích thước chiều ngang
        height: 40, // Kích thước chiều dọc (vuông)
        overflow: "hidden", // Ẩn phần hình ảnh vượt ra ngoài
        borderRadius: 1, // Bo góc, nếu cần
        boxShadow: 3, // Đổ bóng
        flexShrink: 0,
        position: "relative", // Để định vị label phía trên ảnh
      }}
    >
      {quantity > 1 && (
        <Box
          sx={{
            position: "absolute",
            bottom: 0,
            right: 0,
            width: "50%",
            backgroundColor: "rgba(0, 0, 0, 0.6)", // Nền mờ cho label
            color: "white",
            fontSize: 10,
            fontWeight: "bold",
            textAlign: "center",
            padding: "2px 0", // Khoảng cách trên dưới cho label
            zIndex: 1, // Đảm bảo label nằm trên ảnh
          }}
        >
          x{quantity}
        </Box>
      )}
      <img
        src={url} // Thay đường dẫn hình ảnh tại đây
        alt={alt}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover", // Đảm bảo hình ảnh vừa khít khung mà không bị méo
        }}
      />
    </Box>
  );
};

export const ListItemHover = ({ listItems }) => {
  // Lấy 2 sản phẩm đầu tiên
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  return (
    <Box display="flex" gap={1} alignItems="center">
      <Box display="flex" gap={1} flexDirection="column">
        {listItems.map((item) => {
          const imgItemSrc = useValidImage(item.images?.[0]?.link, currentShop?.shopLogo?.link);
          const imgVariantSrc = useValidImage(item.variantImage?.link, imgItemSrc);
          return (
            <Box display="flex" gap={1} key={item.itemsId}>
              <ItemThumbnail
                key={item.itemsId}
                url={imgVariantSrc}
                alt={item.itemsName}
                quantity={item.quantity}
              />
              <Box>
                <Typography>
                  <TruncatedText text={item.itemsName} />
                </Typography>
                <Typography variant="subtitle2">
                  {[item.variantValueOne, item.variantValueTwo, item.variantValueThree]
                    .filter((value) => value != null && value !== "")
                    .join("/")}
                </Typography>
                <Typography variant="subtitle2">{formatMoney(item.price)}đ</Typography>
              </Box>
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};
